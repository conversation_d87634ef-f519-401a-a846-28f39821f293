// The 'using' statements remain largely the same, but would now be organized
// into the different files for each class.
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using Il2Cpp;
using MelonLoader;
using TestLE.Utilities; // Assuming this contains FindHelpers, PlayerHelpers, etc.
using UnityEngine;
using UnityEngine.SceneManagement;
using Random = UnityEngine.Random;

namespace TestLE.Routines.SOLID
{
    #region Core Interfaces and Orchestrator

    /// <summary>
    /// Defines the contract for any executable bot task.
    /// This adheres to the Dependency Inversion Principle, as the main routine
    /// will depend on this abstraction, not concrete implementations.
    /// </summary>
    public interface IGameTask
    {
        /// <summary>
        /// Determines if this task can be executed in the current game state.
        /// </summary>
        /// <returns>True if the task can execute, otherwise false.</returns>
        bool CanExecute();

        /// <summary>
        /// The logic to be executed by the task.
        /// </summary>
        /// <returns>An IEnumerator for the game's coroutine scheduler.</returns>
        IEnumerator Execute();
    }

    /// <summary>
    /// The main orchestrator that replaces the original monolithic class.
    /// It manages and executes a prioritized list of tasks.
    /// This class is OPEN for extension (by adding new IGameTask) but
    /// CLOSED for modification.
    /// </summary>
    public class GameBotRoutine : IEnumerator
    {
        public object? Current { get; private set; }

        private readonly List<IGameTask> _tasks;

        public GameBotRoutine()
        {
            // The order in this list defines the bot's decision-making priority.
            // High-priority tasks (like handling death) come first.
            _tasks = new List<IGameTask>
            {
                new HandleDeathTask(),
                new CompleteMonolithTask(),
                new HandleLootTask(),
                new HandleInteractableTask(),
                new HandleObjectiveTask(),
                new HandleCombatTask(),
                new HandleIdleTask(),
                new DefaultWanderTask() // Fallback task
            };
        }

        public bool MoveNext()
        {
            if (PLAYER == null || CURRENT_ROUTINE == null)
            {
                MelonLogger.Msg("Player or Current Routine is null, cannot proceed.");
                return false;
            }

            // Find the first task that can be executed and run it.
            foreach (var task in _tasks)
            {
                if (task.CanExecute())
                {
                    Current = task.Execute();
                    return true;
                }
            }

            return false; // Should be unreachable if DefaultWanderTask exists
        }

        public void Reset()
        {
            Current = null;
        }
    }

    #endregion

    // ---------------------------------------------------------------------------------

    #region Specialized Handlers

    /// <summary>
    /// Single Responsibility: Manages all logic and state related to stashing items.
    /// </summary>
    public class StashHandler
    {
        private int _currentStashTab = 0;

        public IEnumerator StashAllItems()
        {
            var stashOpener = FindHelpers.FindStashOpener();
            if (stashOpener == null) yield break;

            yield return PlayerHelpers.MoveToForce(stashOpener.transform.position);
            stashOpener.OnUse();
            yield return new WaitForSeconds(0.1f);

            var inventoryUI = FindHelpers.FindInventoryUI();
            var stashNavigable = FindHelpers.FindStashNavigable();
            var stashTabUI = FindHelpers.FindStashTabUI();

            if (inventoryUI?.container == null || stashNavigable == null || stashTabUI == null) yield break;
            
            yield return SelectStashTab(stashNavigable, stashTabUI, 0); // Start at first tab

            var content = inventoryUI.container.GetContent();
            while (content.Count > 0)
            {
                int itemsBeforeMove = content.Count;
                
                foreach (var item in content)
                {
                     inventoryUI.TryQuickMove(item.Position);
                }
                
                yield return new WaitForSeconds(0.1f);

                if (content.Count == 0) break;

                // If no items were moved, go to the next tab.
                if (content.Count >= itemsBeforeMove)
                {
                     MelonLogger.Msg("Stash tab is full, trying next tab.");
                     _currentStashTab++;
                     yield return SelectStashTab(stashNavigable, stashTabUI, _currentStashTab);
                }
            }
        }

        private IEnumerator SelectStashTab(StashTabsNavigable stashNavigable, StashTabbedUIControls stashTabUI, int tabIndex)
        {
            _currentStashTab = tabIndex;
            stashTabUI.SwitchToTab(_currentStashTab);
            stashNavigable.ResetIndex();
            for (var i = 0; i < _currentStashTab; i++)
            {
                stashNavigable.IndexToRight();
            }
            stashNavigable.ClickOnTab();
            yield return new WaitForSeconds(0.1f);
        }
    }
    
    /// <summary>
    /// Centralized location for coroutines used by multiple tasks.
    /// </summary>
    public static class SharedRoutines
    {
        public static IEnumerator GoNextMonolith()
        {
            var stone = FindHelpers.FindMonolithStone();
            if (stone == null) yield break;

            yield return PlayerHelpers.MoveToForce(stone.transform.position);
            stone.ObjectClick(PLAYER.gameObject, true);
            yield return new WaitForSeconds(1f);

            var islands = FindHelpers.FindMonolithIslands();
            if (islands.Count == 0) yield break;

            foreach (var (_, ui) in islands)
            {
                if (ui.island.completed) continue;
                if (ui.island.islandType is not (EchoWebIsland.IslandType.Normal or EchoWebIsland.IslandType.Arena or EchoWebIsland.IslandType.Beacon)) continue;

                var hasConnectionWithCompleted = ui.island.connectedHexes.ToArray().Any(c => islands.GetValueOrDefault(c)?.island.completed ?? false);
                if (hasConnectionWithCompleted)
                {
                    ui.rightClicked();
                    break;
                }
            }

            while (SceneManager.GetActiveScene().name == "M_Rest")
                yield return new WaitForSeconds(1f);

            yield return new WaitForSeconds(1f);
            yield return CURRENT_ROUTINE!.OnNewArea();
        }
    }

    #endregion

    // ---------------------------------------------------------------------------------

    #region Game Task Implementations

    /// <summary>
    /// SRP: Checks for and handles the player's death.
    /// </summary>
    public class HandleDeathTask : IGameTask
    {
        private DeathScreen _deathScreen;

        public bool CanExecute()
        {
            _deathScreen = FindHelpers.FindDeathScreen();
            return _deathScreen != null && _deathScreen.isActiveAndEnabled;
        }

        public IEnumerator Execute()
        {
            MelonLogger.Msg("Death screen found! Respawning...");
            _deathScreen.NormalRespawnClick();
            yield return new WaitForSeconds(1f);
            yield return SharedRoutines.GoNextMonolith();
        }
    }

    /// <summary>
    /// SRP: Handles the entire sequence of completing a monolith.
    /// </summary>
    public class CompleteMonolithTask : IGameTask
    {
        private readonly StashHandler _stashHandler = new();
        
        public bool CanExecute() => FindHelpers.FindMonolithCompleteButton() != null;

        public IEnumerator Execute()
        {
            PlayerHelpers.MoveTo(PLAYER.transform.position); // Stop movement
            yield return new WaitForSeconds(1f);

            yield return CreateAndEnterPortal();

            ResetGlobals(); // Global state reset
            // This task could also notify other tasks to reset their state if needed, e.g., via an event system.

            yield return OpenReward(FindHelpers.FindMonolithCompleteRewardChest);
            yield return OpenReward(FindHelpers.FindMonolithCompleteRewardRock);
            
            yield return CollectXPTomes();
            yield return LootAllGroundItems();
            
            yield return _stashHandler.StashAllItems();
            yield return SharedRoutines.GoNextMonolith();
            MelonLogger.Msg("Monolith completed!");
        }

        private IEnumerator CreateAndEnterPortal()
        {
            PlayerHelpers.UsePortal();
            yield return new WaitForSeconds(2f);
            
            var portal = FindHelpers.FindMonolithPortal();
            if (portal == null) yield break;
            
            yield return LootAllGroundItems(); // Loot before leaving
            yield return PlayerHelpers.MoveToForce(portal.transform.position);
            portal.ObjectClick(PLAYER.gameObject, true);
            yield return new WaitForSeconds(1f);
        }

        private IEnumerator OpenReward<T>(Func<(T obj, bool isActive)> findAction) where T : WorldObjectClickListener
        {
            var reward = findAction();
            if (reward.obj != null && reward.isActive)
            {
                yield return PlayerHelpers.MoveToForce(reward.obj.transform.position);
                reward.obj.ObjectClick(PLAYER.gameObject, true);
                yield return new WaitForSeconds(1.5f); // Wait for loot to drop
            }
        }

        private IEnumerator LootAllGroundItems()
        {
            // Wait for any potential loot drops to finish spawning
            if (LAST_GROUND_ITEM_DROP != DateTime.MinValue && (DateTime.Now - LAST_GROUND_ITEM_DROP).TotalSeconds < 1)
                yield return new WaitForSeconds(1f);

            while (GROUND_ITEMS.Count > 0)
            {
                var groundItem = GROUND_ITEMS.FirstOrDefault();
                if (groundItem == null) { GROUND_ITEMS.RemoveAt(0); continue; }
                
                yield return groundItem.MoveToItem();
                groundItem.Pickup();
                yield return new WaitForSeconds(0.1f);
            }
        }

        private IEnumerator CollectXPTomes()
        {
            var tomes = FindHelpers.FindGroundXPTomes();
            foreach (var t in tomes)
            {
                yield return PlayerHelpers.MoveToForce(t.transform.position);
            }
        }
    }
    
    /// <summary>
    /// SRP: Finds and picks up loot from the ground.
    /// </summary>
    public class HandleLootTask : IGameTask
    {
        public bool CanExecute()
        {
            if (GROUND_ITEMS.Count == 0) return false;
            
            // Don't loot if an enemy is dangerously close
            var (enemy, distance) = FindHelpers.FindNearestEnemy(PLAYER.transform.position, 10);
            return enemy == null || distance > 3f;
        }

        public IEnumerator Execute()
        {
            var groundItem = GROUND_ITEMS.FirstOrDefault();
            if (groundItem == null)
            {
                GROUND_ITEMS.RemoveAt(0);
                yield break;
            }

            yield return groundItem.MoveToItem();
            groundItem.Pickup();
        }
    }

    /// <summary>
    /// SRP: Finds and interacts with world objects like shrines.
    /// </summary>
    public class HandleInteractableTask : IGameTask
    {
        private WorldObjectClickListener _targetInteractable;

        public bool CanExecute()
        {
            // Simplified logic to find the closest interactable (e.g., a good shrine)
            _targetInteractable = GOOD_SHRINES
                .Where(s => s != null && Vector3.Distance(PLAYER.transform.position, s.transform.position) <= 20f)
                .OrderBy(s => Vector3.Distance(PLAYER.transform.position, s.transform.position))
                .FirstOrDefault();

            return _targetInteractable != null;
        }

        public IEnumerator Execute()
        {
            yield return PlayerHelpers.MoveToForce(_targetInteractable.transform.position, _targetInteractable.interactionRange * 0.8f);
            _targetInteractable.ObjectClick(PLAYER.gameObject, true);
            
            // Invalidate the object after interaction
            GOOD_SHRINES.Remove(_targetInteractable); 
            INTERACTABLES.Remove(_targetInteractable);
        }
    }
    
    /// <summary>
    /// SRP: Handles moving towards and completing monolith objectives.
    /// </summary>
    public class HandleObjectiveTask : IGameTask
    {
        private MonolithObjective _objective;

        public bool CanExecute()
        {
            if (MONOLITH_OBJECTIVES.Count == 0) return false;
            _objective = MONOLITH_OBJECTIVES.FirstOrDefault();
            return _objective != null;
        }

        public IEnumerator Execute()
        {
            var enemyObjective = _objective.GetEnemyObjective();
            if (enemyObjective != null)
            {
                PlayerHelpers.MoveTo(enemyObjective.transform.position);
                yield return new WaitForSeconds(0.3333f);
                yield break;
            }

            var clickObjective = _objective.GetClickObjective();
            if (clickObjective != null)
            {
                var objectivePos = clickObjective.transform.position;
                PlayerHelpers.MoveTo(objectivePos);
                yield return new WaitForSeconds(0.3333f);

                if (Vector3.Distance(PLAYER.transform.position, clickObjective.transform.position) <= clickObjective.interactionRange)
                {
                    clickObjective.ObjectClick(PLAYER.gameObject, true);
                }
                yield break;
            }
            
            // If objective is neither, remove it and let the next task run
            MONOLITH_OBJECTIVES.Remove(_objective);
        }
    }

    /// <summary>
    /// SRP: Manages all combat logic.
    /// </summary>
    public class HandleCombatTask : IGameTask
    {
        private Enemy _targetEnemy;
        private float _distance;
        private readonly Dictionary<Enemy, int> _enemyInactiveFailSafe = new();
        
        public bool CanExecute()
        {
            var (enemy, distance) = FindHelpers.FindNearestEnemy(PLAYER.transform.position, CURRENT_ROUTINE.CombatDistance);
            if (enemy == null) return false;

            _targetEnemy = enemy;
            _distance = distance;
            return true;
        }

        public IEnumerator Execute()
        {
            if (_targetEnemy.Data.gameObject.active)
            {
                yield return CURRENT_ROUTINE!.Run(_targetEnemy, _targetEnemy.Data.transform, _distance);
            }
            else // Handle inactive/dead enemies that aren't cleaned up
            {
                if (_distance <= 3f)
                {
                    _enemyInactiveFailSafe[_targetEnemy] = _enemyInactiveFailSafe.GetValueOrDefault(_targetEnemy) + 1;
                    if (_enemyInactiveFailSafe[_targetEnemy] >= 10)
                    {
                        _targetEnemy.RemoveEnemy();
                        _enemyInactiveFailSafe.Remove(_targetEnemy);
                        yield break;
                    }
                }
                PlayerHelpers.MoveTo(_targetEnemy.Data.transform.position);
                yield return new WaitForSeconds(0.3333f);
            }
        }
    }

    /// <summary>
    /// SRP: Handles the case where the player is idle.
    /// </summary>
    public class HandleIdleTask : IGameTask
    {
        private float _idleTime;
        private const float IDLE_SECONDS_THRESHOLD = 5f;
        
        public bool CanExecute()
        {
            if (PLAYER.movingState.myAgent.velocity.magnitude <= 0.1f)
            {
                _idleTime += Time.deltaTime;
                if (_idleTime >= IDLE_SECONDS_THRESHOLD) return true;
            }
            else
            {
                _idleTime = 0;
            }
            return false;
        }

        public IEnumerator Execute()
        {
            _idleTime = 0; // Reset timer
            if (UnityHelpers.RandomPointOnNavMesh(PLAYER.transform.position, 10f, out var movePos))
                PlayerHelpers.MoveTo(movePos);
            else
                PlayerHelpers.MoveTo(PLAYER.transform.position + new Vector3(Random.Range(-10, 10), 0, Random.Range(-10, 10)));
            
            yield return new WaitForSeconds(2f);
        }
    }
    
    /// <summary>
    /// SRP: A fallback task to ensure the bot is always doing something.
    /// </summary>
    public class DefaultWanderTask : IGameTask
    {
        public bool CanExecute() => true; // Always able to run if no other task can

        public IEnumerator Execute()
        {
            // If no enemies, objectives, or loot, just move a little
            PlayerHelpers.MoveTo(PLAYER.transform.position + new Vector3(Random.Range(-5, 5), 0, Random.Range(-5, 5)));
            yield return new WaitForSeconds(1f);
        }
    }
    #endregion
}